import { DataContract } from 'data-contract';

export function explore_table_structure(contract: DataContract, { tableName }: { tableName: string }) {
    const table = contract.tables[tableName];
    if (!table) {
        return { error: `Table not found: ${tableName}` };
    }
    // Return a copy to avoid callers modifying the original contract object
    return { ...table };
}

export function explain_column_meaning(contract: DataContract, { tableName, columnName }: { tableName: string, columnName: string }) {
    const table = contract.tables[tableName];
    if (!table) {
        return { error: `Table not found: ${tableName}` };
    }

    const column = table.columns[columnName];
    if (!column) {
        return { error: `Column not found: ${columnName} in table ${tableName}` };
    }

    const { businessName, description, businessRules } = column;
    // Return a new object with the relevant fields
    return { businessName, description, businessRules: businessRules || [] };
}

export function expand_abbreviations(contract: DataContract, { abbreviation }: { abbreviation: string }) {
    const expansion = contract.abbreviations?.[abbreviation];
    if (!expansion) {
        return { error: `Abbreviation not found: ${abbreviation}` };
    }
    return { expansion };
}
