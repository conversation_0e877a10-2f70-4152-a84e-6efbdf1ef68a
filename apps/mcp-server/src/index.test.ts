jest.mock('@modelcontextprotocol/sdk', () => {
  // Mock the McpServer class
  return {
    McpServer: class McpServer {
      constructor(private options: { app: any }) {}
      tool(name: string, description: string, handler: any) {
        // Mock the .tool() method to actually register a route on the Hono app
        this.options.app.post(`/mcp/tools/${name}`, handler);
      }
    },
  };
});

import { app } from './index'; // Import the Hono app instance

describe('MCP Server Index', () => {
  // Test suite for the server's tool endpoints
  describe('Tool Endpoints', () => {
    // Tests for the explore_table_structure tool
    describe('POST /mcp/tools/explore_table_structure', () => {
      it('should return table structure for a valid request', async () => {
        const res = await app.request('/mcp/tools/explore_table_structure', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ tableName: 'CUST_MST' }),
        });
        expect(res.status).toBe(200);
        const json = await res.json();
        expect(json.businessName).toBe('Customer Master');
        expect(json.columns).toHaveProperty('c_id');
      });

      it('should return 400 for a request with a missing required parameter', async () => {
        const res = await app.request('/mcp/tools/explore_table_structure', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}), // Missing tableName
        });
        expect(res.status).toBe(400);
        const json = await res.json();
        expect(json.error).toBe('Validation failed');
        expect(json.messages[0]).toContain('Required');
      });
    });

    // Tests for the explain_column_meaning tool
    describe('POST /mcp/tools/explain_column_meaning', () => {
        it('should return 400 for a request with an invalid parameter type', async () => {
            const res = await app.request('/mcp/tools/explain_column_meaning', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ tableName: 'CUST_MST', columnName: 123 }), // columnName should be a string
            });
            expect(res.status).toBe(400);
            const json = await res.json();
            expect(json.error).toBe('Validation failed');
            expect(json.messages[0]).toContain('Expected string, received number');
        });
    });

     // Tests for the expand_abbreviations tool
     describe('POST /mcp/tools/expand_abbreviations', () => {
        it('should return an expansion for a valid abbreviation', async () => {
            const res = await app.request('/mcp/tools/expand_abbreviations', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ abbreviation: 'CUST' }),
            });
            expect(res.status).toBe(200);
            const json = await res.json();
            expect(json.expansion).toBe('Customer');
        });
     });
  });
});
