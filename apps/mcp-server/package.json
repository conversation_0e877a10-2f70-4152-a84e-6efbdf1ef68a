{"name": "mcp-server", "version": "0.1.0", "private": true, "scripts": {"build": "tsc -b", "dev": "node --watch dist/index.js", "start": "node dist/index.js", "test": "jest", "audit-schema": "npm run build && node dist/audit.js", "docgen": "npm run build && node dist/docgen.js"}, "dependencies": {"@hono/node-server": "^1.11.1", "@hono/zod-validator": "^0.7.2", "@types/js-yaml": "^4.0.9", "data-contract": "1.0.0", "hono": "^4.5.0", "ioredis": "^5.4.1", "js-yaml": "^4.1.0", "pg": "^8.16.3", "zod": "^4.0.16"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "@types/jest": "^29.5.14", "@types/node": "^20.14.2", "@types/pg": "^8.15.5", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "ts-jest": "^29.1.5", "typescript": "^5.4.5"}, "peerDependencies": {"@modelcontextprotocol/sdk": "^1.17.1"}}