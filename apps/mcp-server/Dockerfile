# ---- Base ----
# Use a specific Node.js version for reproducibility.
FROM node:24.5.0-alpine AS base
WORKDIR /usr/src/app

# ---- Dependencies ----
# This stage is dedicated to installing all dependencies, including devDependencies,
# which are required for the build and test stages.
FROM base AS deps
COPY package.json package-lock.json* ./
# The --no-optional flag can be used to skip optional dependencies if any.
RUN npm install

# ---- Build ----
# This stage builds the TypeScript source code into JavaScript.
FROM base AS build
# Copy dependencies from the 'deps' stage.
COPY --from=deps /usr/src/app/node_modules ./node_modules
# Copy the rest of the application's source code.
COPY . .
# Run the build script defined in package.json.
RUN npm run build

# ---- Release ----
# This is the final stage that creates the production image.
# It starts from the base image to keep it lean.
FROM base AS release
# Copy the package.json to the release stage.
COPY package.json ./
# Install only production dependencies. This significantly reduces the size of the final image.
RUN npm install --omit=dev
# Copy the built application from the 'build' stage.
COPY --from=build /usr/src/app/dist ./dist

# Expose the port the application will run on.
EXPOSE 3000
# The command to start the application.
CMD ["npm", "start"]
