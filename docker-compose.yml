version: '3.8'
services:
  mcp-server:
    build:
      context: ./apps/mcp-server
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - redis
      - db
    env_file:
      - .env

  redis:
    image: "redis:7.2-alpine"
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  db:
    image: postgres:14-alpine
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    env_file:
      - .env

volumes:
  redis_data:
  postgres_data:
